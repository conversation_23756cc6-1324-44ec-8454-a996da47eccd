import React from 'react';
import {
  YidaPage,
  Page,
  RootHeader,
  RootContent,
  FormContainer,
  TableField,
  TextField,
} from 'yida';

export default class Root extends YidaPage {
  constructor() {
    super();

    this.state = {};
  }

  /**
   * 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系宜搭平台的技术支持获得服务（可能收费）。
   * 我们可以用 JS 面板来开发一些定制度高功能，比如：调用阿里云接口用来做图像识别、上报用户使用数据（如加载完成打点）等等。
   * 你可以点击面板上方的 「使用帮助」了解。
   */
  // 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。
  didMount = () => {
    console.log(`「页面 JS」：当前页面地址 ${location.href}`);
    // console.log(`「页面 JS」：当前页面 id 参数为 ${this.state.urlParams.id}`);
    // 更多 this 相关 API 请参考：https://www.yuque.com/yida/support/ocmxyv#OCEXd
    // document.title = window.loginUser.userName + ' | 宜搭';
  };

  render() {
    return (
      <Page
        titleDesc={{ type: 'i18n', zh_CN: '标题描述', en_US: 'title' }}
        pageStyle={{ backgroundColor: '#f2f3f5' }}
        contentMargin="12"
        contentPadding="20"
        contentPaddingMobile="0"
        titleBg="https://img.alicdn.com/imgextra/i2/O1CN0143ATPP1wIa9TrVvzN_!!6000000006285-2-tps-3360-400.png_.webp"
        sizePc="medium"
        labelAlignPc="top"
        contentMarginMobile="12"
        className="page_mdzrbath"
        labelWidthPc="130px"
        contentBgColor="white"
        titleName={{ type: 'i18n', zh_CN: '标题名称', en_US: 'title' }}
        titleColor="light"
        backgroundColorCustom="#f1f2f3"
        showTitle={true}
        templateVersion="1.0.0"
        contentBgColorMobile="white"
        labelWeightMobile="normal"
        labelWidthMobile="80px"
        labelWeightPc="normal"
        labelAlignMobile="top"
      >
        <RootHeader />

        <RootContent>
          <FormContainer
            beforeSubmit={false}
            submitText={{ type: 'i18n', zh_CN: '提交', en_US: 'Submit' }}
            labelAlign="top"
            columns={1}
            stageText={{ type: 'i18n', zh_CN: '暂存', en_US: 'Stage' }}
            afterFormDataInit={false}
            submitAndNewText={{
              type: 'i18n',
              zh_CN: '提交并继续',
              en_US: 'Submit and New',
            }}
            onProcessActionValidate={false}
            fieldId="formContainer_mdzrb8nz"
            afterSubmit={false}
          >
            <TableField
              showIndex={true}
              __useMediator="value"
              pageSize={50}
              mobileFreezeColumnStartCounts="0"
              __gridSpan={1}
              addButtonText={{
                type: 'i18n',
                zh_CN: '新增一项',
                en_US: 'Add item',
              }}
              addButtonPosition="bottom"
              valueType="custom"
              labelTextAlign="left"
              delButtonText={{ type: 'i18n', zh_CN: '删除', en_US: 'Remove' }}
              useCustomColumnsWidth={false}
              moveUp={{ type: 'i18n', zh_CN: '上移', en_US: 'Up' }}
              __designerDevice="pc"
              fieldId="tableField_me6k8our"
              maxItems={999999}
              showActions={true}
              tableLayout="fixed"
              enableSummary={false}
              visibility={['PC', 'MOBILE']}
              indexName={{ type: 'i18n', zh_CN: '', en_US: '' }}
              showDelAction={true}
              __category__="form"
              moveDown={{ type: 'i18n', zh_CN: '下移', en_US: 'Down' }}
              labelColSpan={4}
              minItems={0}
              complexValue={{ complexType: 'custom', formula: '' }}
              defaultCollapseStatus={true}
              isFreezeOperateColumn={true}
              actions={[]}
              copyButtonText={{ type: 'i18n', zh_CN: '复制', en_US: 'Copy' }}
              addButtonBehavior="NORMAL"
              linkage=""
              enableBatchDelete={false}
              enableExport={true}
              actionsColumnWidth={70}
              theme="split"
              behavior="NORMAL"
              showSortable={false}
              mobileLayout="TILED"
              dataEntryMode={false}
              submittable="DEFAULT"
              showCopyAction={false}
              filterEmptyRowData={true}
              label={{ type: 'i18n', zh_CN: '子表单', en_US: 'Table Field' }}
              showTableHead={true}
              pcFreezeColumnStartCounts="0"
              layout="TABLE"
              showDeleteConfirm={true}
              enableImport={true}
              labelAlign="top"
              enableVirtualScroll={true}
              enablePagination={false}
              enableItemLimit={false}
              virtualScrollHeight={400}
            >
              <TextField
                __useMediator="value"
                hasClear={true}
                validationType="text"
                __gridSpan={1}
                linkage=""
                tips={{ en_US: '', zh_CN: '', type: 'i18n' }}
                valueType="custom"
                labelTextAlign="left"
                placeholder={{
                  type: 'i18n',
                  zh_CN: '请输入',
                  en_US: 'Please enter',
                }}
                behavior="NORMAL"
                value={{ type: 'i18n', zh_CN: '', en_US: '' }}
                validation={[]}
                hasLimitHint={false}
                fieldId="textField_me6k8ous"
                autoHeight={false}
                visibility={['PC', 'MOBILE']}
                dataEntryMode={false}
                submittable="DEFAULT"
                label={{ type: 'i18n', zh_CN: '单行文本', en_US: 'Text Field' }}
                __category__="form"
                rows={4}
                labelColSpan={4}
                scanCode={{ enabled: false, type: 'all', editable: true }}
                complexValue={{
                  complexType: 'custom',
                  formula: '',
                  value: { en_US: '', zh_CN: '', type: 'i18n' },
                }}
                labelAlign="top"
                variable=""
                formula=""
                maxLength={200}
                isCustomStore={true}
                size="medium"
              />
            </TableField>
          </FormContainer>
        </RootContent>
      </Page>
    );
  }
}
