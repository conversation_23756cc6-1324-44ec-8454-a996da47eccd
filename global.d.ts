/// <reference types="react" />
declare module 'yida' {
  import React from 'react';

  // 国际化文案词条
  export interface II18n {
    type: 'i18n';
    key?: string;
    zh_CN: string;
    en_US: string;
  }

  // 通用图标类型
  export type IIcon =
    | 'set'
    | 'warning'
    | 'error'
    | 'success'
    | 'prompt'
    | 'upload'
    | 'download'
    | 'search'
    | 'info'
    | 'close'
    | 'check'
    | 'loading'
    | 'empty'
    | 'delete'
    | 'edit'
    | 'refresh';

  // 组件公共属性
  export interface ICompProps {
    /**
     * 自定义类名
     */
    customClassName?: string;
    /**
     * 自定义样式, 组件自定样式，需要用:root选择器包裹，例如：
     *
     * :root {
     *  color: #000;
     *  background-color: #fff;
     * }
     *
     */
    __style__?: string;
    /**
     * 字段唯一标识，形式为：componentname_uuid模式，其中componentName为组件的名称，uuid为8位随机码包含数字和小写字母
     */
    fieldId: string;
  }

  // 校验规则类型
  type ValidationRuleType =
    | 'required'
    | 'minLength'
    | 'maxLength'
    | 'minValue'
    | 'maxValue'
    | 'customValidate';

  // 基础校验规则接口
  interface IBaseValidationRule {
    type: ValidationRuleType;
    message: II18n;
  }

  // 带参数的校验规则接口
  interface IParamValidationRule extends IBaseValidationRule {
    type: 'minLength' | 'maxLength' | 'minValue' | 'maxValue';
    param: string;
  }

  // 自定义校验规则接口
  interface ICustomValidationRule extends IBaseValidationRule {
    type: 'customValidate';
    param: {
      type: 'js';
      source: string;
    };
  }

  // 必填校验规则接口
  interface IRequiredValidationRule extends IBaseValidationRule {
    type: 'required';
  }

  // 组合所有校验规则类型
  type ValidationRule =
    | IRequiredValidationRule
    | IParamValidationRule
    | ICustomValidationRule;

  // 校验规则数组类型
  export type Validation = ValidationRule[];

  export interface IFieldProps extends ICompProps {
    /**
     * 标签文本
     * @default ''
     */
    label: string;

    // /**
    //  * 标签对齐方式
    //  * @default 'left'
    //  */
    // labelAlign: "left" | "top";

    // /**
    //  * 标题宽度，占位宽度栅格总数为12
    //  *
    //  */
    // labelColSpan: number;

    // /**
    //  * 标题偏移，占位宽度栅格总数为12
    //  *
    //  */
    // labelColOffset: number;

    // /**
    //  * 内容宽度，占位宽度栅格总数为12
    //  *
    //  */
    // wrapperColSpan: number;

    // /**
    //  * 内容偏移，占位宽度栅格总数为12
    //  *
    //  */
    // wrapperColOffset: number;

    // /**
    //  * 标题对齐
    //  *
    //  */
    // labelTextAlign: "left" | "right";

    /**
     * 占位提示
     *
     */
    placeholder: string;

    /**
     * 标题提示
     *
     */
    labelTipsTypes: 'none' | 'text' | 'render';

    /**
     * 提示图标
     *
     */
    labelTipsIcon?: IIcon;

    /**
     * 标题提示
     *
     */
    labelTips: string;

    /**
     * 提示内容
     *
     */
    labelTipsText: string;

    /**
     * 提示内容
     *
     */
    labelTipsRender: (props: any) => void;

    /**
     * 组件尺寸
     */
    size: 'small' | 'medium' | 'large';

    /**
     * 提示文字
     */
    tips: string;

    /**
     * 组件展示状态， NORMAL-控件的基本功能均可操作，DISABLED-UI呈现禁用效果的特定样式，READONLY-仅显示控件的预置数据的内容，控件不可编辑，HIDDEN-运行态时不显示。对应表单数据默认不会提交，可在高级-数据提交中进行配置
     */
    behavior: 'NORMAL' | 'DISABLED' | 'READONLY' | 'HIDDEN';

    /**
     * 显示设备
     */
    visibility: Array<'PC' | 'MOBILE'>;

    /**
     * 校验规则
     */
    validation: Validation;
  }

  // 数据源类型
  export interface IDataSource {
    /**
     * 选项文本
     */
    text: string;

    /**
     * 选项值
     */
    value: string;

    /**
     * 是否禁用
     * @default false
     */
    disable?: boolean;

    /**
     * 是否默认选中
     * @default false
     */
    defaultChecked?: boolean;
  }

  export const PageRoot: React.FC<ICompProps>;

  interface IDataSourceInstance {
    load: (params?: Record<string, any>) => Promise<any>;
  }

  export interface IDialogProps {
    type?: 'alert' | 'confirm' | 'show';
    title?: string;
    content?: string;
    hasMask?: boolean;
    footer?: boolean;
    footerAlign?: 'left' | 'center' | 'right';
    footerActions?: Array<
      ['cancel', 'ok'] | ['ok', 'cancel'] | ['ok'] | ['cancel']
    >;
    onOk?: () => void;
    onCancel?: () => void;
  }

  interface IToastProps {
    type?: 'success' | 'warning' | 'error' | 'notice' | 'help' | 'loading';
    title: string;
    size?: 'medium' | 'large';
    duration?: number;
  }

  interface IComponentBehavior {
    NORMAL: 'NORMAL';
    READONLY: 'READONLY';
    DISABLED: 'DISABLED';
    HIDDEN: 'HIDDEN';
  }

  interface IValidateRule {
    type: string;
    param?: any;
    message?: string;
  }

  export interface IRouterUtils {
    push: (
      path: string,
      params?: Record<string, any>,
      blank?: boolean,
      isUrl?: boolean,
      type?: string,
    ) => void;
    replace: (path: string, params?: Record<string, any>) => void;
    getQuery: (
      key?: string,
      queryStr?: string,
    ) => Record<string, string> | string | undefined;
    stringifyQuery: (params: Record<string, any>) => string;
  }

  interface IComponentInstance {
    get: (prop: string) => any;
    set: (prop: string, value: any) => void;
    getValue: () => any;
    setValue: (
      value: any,
      options?: {
        doNotValidate?: boolean;
        formatted?: boolean;
        triggerChange?: boolean;
      },
    ) => void;
    reset: (toDefault?: boolean) => void;
    getBehavior: () => keyof IComponentBehavior;
    setBehavior: (behavior: keyof IComponentBehavior) => void;
    resetBehavior: () => void;
    validate: (
      callback?: (
        errors: string[] | null,
        values: Record<string, any> | null,
      ) => void,
    ) => void;
    disableValid: () => void;
    enableValid: (doValidate?: boolean) => void;
    setValidation: (rules: IValidateRule[], doValidate?: boolean) => void;
    resetValidation: (doValidate?: boolean) => void;
    show?: () => void;
    hide?: () => void;
  }

  export interface IUtils {
    dialog: (props: IDialogProps) => { hide: () => void };
    formatter: (
      type: 'date' | 'money' | 'cnmobile' | 'card',
      value: string | Date,
      format?: string,
    ) => string;
    getDateTimeRange: (
      when?: Date | number,
      type?:
        | 'year'
        | 'month'
        | 'week'
        | 'day'
        | 'date'
        | 'hour'
        | 'minute'
        | 'second',
    ) => [number, number];
    getLocale: () => string;
    getLoginUserId: () => string;
    getLoginUserName: () => string;
    isMobile: () => boolean;
    isSubmissionPage: () => boolean;
    isViewPage: () => boolean;
    loadScript: (url: string) => Promise<void>;
    openPage: (url: string) => void;
    previewImage: (props: { current: string }) => void;
    toast: (props: IToastProps) => () => void;
    router: IRouterUtils;
  }

  export interface IPage {
    dataSourceMap?: Record<string, IDataSourceInstance>;
    reloadDataSource?: () => Promise<void>;
    utils?: IUtils;
    $?: (fieldId: string) => IComponentInstance;
  }

  export const YidaPage: React.ComponentType<IPage>;

  interface IPageProps extends ICompProps {
    /**
     * 模版名称
     *
     */
    templateName?: string;

    /**
     * 模版版本
     *
     * @default 1.0.0
     */
    templateVersion?: string;

    /**
     * Body 样式设置
     * 点击 ? 查看样式设置器用法指南
     * @default {"backgroundColor":"#f2f3f5"}
     */
    pageStyle?: any;

    /**
     * 顶部banner布局风格类型，当设置顶部banner时，需要设置
     *
     * 可选择以下数组中的一组配置：[{"text":"无","value":"","img":"https://img.alicdn.com/imgextra/i1/O1CN01FgbIPV1oC9kacE3O5_!!6000000005188-2-tps-186-100.png"},{"text":"高级氛围","value":"atmosphereA","img":"https://img.alicdn.com/imgextra/i3/O1CN01Qghq8u29f6iVVfSL2_!!6000000008094-2-tps-186-100.png"},{"text":"AdvancedAtmosphereB","value":"atmosphereB","img":"https://img.alicdn.com/imgextra/i4/O1CN01UbwSR41Vv3sgPcEFm_!!6000000002714-2-tps-186-100.png"},{"text":"AdvancedAtmosphereC","value":"atmosphereC","img":"https://img.alicdn.com/imgextra/i4/O1CN01cb4A2M1sM0jUbA0mA_!!6000000005751-2-tps-186-100.png"},{"text":"AdvancedAtmosphereD","value":"atmosphereD","img":"https://img.alicdn.com/imgextra/i3/O1CN01yNlkMk1Qe35lDXBo2_!!6000000002000-2-tps-186-100.png"},{"text":"AdvancedAtmosphereE","value":"atmosphereE","img":"https://img.alicdn.com/imgextra/i1/O1CN01jDv3nR1SE3qle4Xca_!!6000000002214-2-tps-186-100.png"}]
     */
    titleStyle?: any;

    /**
     * 标题名称
     * 建议不超过10个汉字
     * @default {"zh_CN":"标题名称","en_US":"title","type":"i18n"}
     */
    titleName?: string;

    /**
     * 标题描述
     * 建议不超过70个汉字
     * @default {"zh_CN":"标题描述","en_US":"title","type":"i18n"}
     */
    titleDesc?: string;

    /**
     * 标题颜色
     *
     * 选项使用说明：dark - undefined;light - undefined
     * @default light
     */
    titleColor?: 'dark' | 'light';

    /**
     * 氛围图片
     *
     * @default https://img.alicdn.com/imgextra/i2/O1CN0143ATPP1wIa9TrVvzN_!!6000000006285-2-tps-3360-400.png_.webp
     */
    titleBg?: any;

    /**
     * 背景颜色
     *
     * @default #f1f2f3
     */
    backgroundColorCustom?: any;
  }

  //
  export const Page: React.ComponentType<IPageProps>;

  interface IFormContainerProps extends ICompProps {
    /**
     * 公式校验
     * 点击 ? 查看公式校验使用帮助
     */
    validators?: any;

    /**
     * 服务校验
     * 点击 ? 查看第三方服务使用帮助
     */
    serviceValidators?: any;

    /**
     * 自定义代码服务校验
     * 点击 ? 查看第三方服务使用帮助
     */
    cloudcodeValidators?: any;

    /**
     * 公式执行
     * 点击「?」查看业务关联规则使用帮助
     */
    associationRules?: any;

    /**
     * 服务执行
     * 点击 ? 查看第三方服务使用帮助
     */
    serviceRules?: any;

    /**
     * 服务二开执行
     * 点击 ? 查看第三方服务使用帮助
     */
    cloudcodeRules?: any;

    /**
     * 提交文案
     *
     */
    submitText?: string;

    /**
     * 表单提交前
     *
     */
    beforeSubmit?: (props: any) => void;

    /**
     * 表单提交后
     *
     */
    afterSubmit?: (props: any) => void;

    /**
     * 表单数据源
     * 点击查看数据结构
     */
    dataSource?: any;
  }

  //
  export const FormContainer: React.ComponentType<IFormContainerProps>;

  interface ITextFieldProps extends ICompProps {
    /**
     * 占位提示
     *
     * @default {"zh_CN":"请输入","en_US":"Please enter","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - 自定义;variable - undefined;formula - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'variable' | 'formula' | 'linkage';

    /**
     *
     *
     * @default {"type":"i18n","en_US":"","zh_CN":""}
     */
    value?: string;

    /**
     *
     *
     */
    formula?: any;

    /**
     * 多行文本高度
     *
     * @default 4
     */
    rows?: number;

    /**
     * 多行文本自动高度
     *
     */
    autoHeight?: boolean;

    /**
     * 格式
     *
     * 选项使用说明：text - undefined;mobile - undefined;email - undefined;url - undefined;chineseID - undefined;password - undefined
     * @default text
     */
    validationType?:
      | 'text'
      | 'mobile'
      | 'email'
      | 'url'
      | 'chineseID'
      | 'password';

    /**
     * 清除按钮
     *
     * @default true
     */
    hasClear?: boolean;

    /**
     * 显示计数器
     *
     */
    hasLimitHint?: boolean;

    /**
     * 字数上限
     * 仅做提示，如果想做校验请在上方「校验」配置编辑最大长度
     * @default 200
     */
    maxLength?: number;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * textField onChange
     * @param value 当前值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onFocus 获取焦点
     * * textField onFocus
     */
    onFocus?: () => void;

    /**
     * onBlur 失去焦点
     * * textField onBlur
     */
    onBlur?: () => void;

    /**
     * onPaste 粘贴时(仅PC端)
     * * textField onPaste
     */
    onPaste?: (e: any) => void;

    /**
     * onKeyDown 键盘按下(仅PC端)
     * * textField onKeyDown
     */
    onKeyDown?: () => void;

    /**
     * onPressEnter 按下回车(仅PC端)
     * * textField onPressEnter
     */
    onPressEnter?: () => void;

    /**
     * onScanCodeSuccess 扫码成功后
     * * 扫码成功后，可在此修改扫码结果
     */
    onScanCodeSuccess?: (text: any) => void;

    /**
     * onScanCodeError 扫码失败后
     * * textField onScanCodeError
     */
    onScanCodeError?: (errorMsg: any) => void;
  }

  //
  export const TextField: React.ComponentType<ITextFieldProps>;

  interface INumberFieldProps extends ICompProps {
    /**
     * 占位提示
     *
     * @default {"zh_CN":"请输入数字","en_US":"Please enter a number","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - 自定义;formula - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'formula' | 'linkage';

    /**
     *
     *
     */
    formula?: any;

    /**
     * 单位
     *
     * @default {"type":"i18n","zh_CN":"","en_US":""}
     */
    innerAfter?: string;

    /**
     * 小数位数
     *
     */
    precision?: number;

    /**
     * 千位分隔
     *
     */
    thousandsSeparators?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * numberField onChange
     * @param value 当前值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onKeyDown 键盘按下
     * * numberField onKeyDown
     */
    onKeyDown?: () => void;

    /**
     * onFocus 焦点获得
     * * numberField onFocus
     */
    onFocus?: () => void;

    /**
     * onBlur 焦点失去
     * * numberField onBlur
     */
    onBlur?: () => void;

    /**
     * onCorrect 数据订正后
     * * numberField onCorrect
     */
    onCorrect?: ({ currentValue, oldValue }: any) => void;
  }

  //
  export const NumberField: React.ComponentType<INumberFieldProps>;

  interface IRadioFieldProps extends ICompProps {
    /**
     * 展示形状
     *
     * 选项使用说明：default - 默认;button - 按钮
     * @default default
     */
    shape?: 'default' | 'button';

    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'linkage';

    /**
     *
     *
     */
    value?: any;

    /**
     *
     *
     */
    formula?: any;

    /**
     * 排列方式
     * 仅 PC 端生效
     * 选项使用说明：hoz - 水平排列;ver - 垂直排列
     * @default hoz
     */
    itemDirection?: 'hoz' | 'ver';

    /**
     * 支持反选
     * 再次点击选项可取消选择
     */
    supportInverse?: boolean;

    /**
     * 选项类型
     *
     * 选项使用说明：custom - undefined;relate - undefined;linkage - undefined;remote - undefined
     * @default custom
     */
    dataSourceType?: 'custom' | 'relate' | 'linkage' | 'remote';

    /**
     * 自定义选项
     * 数据格式
     * @default [{"text":{"zh_CN":"选项一","en_US":"Option 1","type":"i18n"},"value":"选项一","sid":"serial_khe7yak4","disable":false,"defaultChecked":false},{"text":{"zh_CN":"选项二","en_US":"Option 2","type":"i18n"},"value":"选项二","sid":"serial_khe7yak5","disable":false,"defaultChecked":false},{"text":{"zh_CN":"选项三","en_US":"Option 3","type":"i18n"},"value":"选项三","sid":"serial_khe7yak6","disable":false,"defaultChecked":false}]
     */
    dataSource?: IDataSource[];

    /**
     * 网关数据
     * 点击查看如何使用网关数据
     */
    remote?: string;

    /**
     * 关联表权限组数据过滤
     * 开启后，关联的选项数据将过滤掉用户所在权限组无法查看的实例的数据
     */
    reusePrivilege?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * radioField onChange
     * @param value 被选中的选项的值
     */
    onChange?: ({ value }: any) => void;
  }

  //
  export const RadioField: React.ComponentType<IRadioFieldProps>;

  interface ICheckboxFieldProps extends ICompProps {
    /**
     * 排列方式
     * 仅 PC 端生效
     * 选项使用说明：hoz - 水平排列;ver - 垂直排列
     * @default hoz
     */
    itemDirection?: 'hoz' | 'ver';

    /**
     * 选项类型
     *
     * 选项使用说明：custom - undefined;relate - undefined;linkage - undefined;remote - undefined
     * @default custom
     */
    dataSourceType?: 'custom' | 'relate' | 'linkage' | 'remote';

    /**
     * 自定义选项
     * 数据格式
     * @default [{"text":{"zh_CN":"选项一","en_US":"Option 1","type":"i18n"},"value":"选项一","sid":"serial_khe7yak4","disable":false,"defaultChecked":false},{"text":{"zh_CN":"选项二","en_US":"Option 2","type":"i18n"},"value":"选项二","sid":"serial_khe7yak5","disable":false,"defaultChecked":false},{"text":{"zh_CN":"选项三","en_US":"Option 3","type":"i18n"},"value":"选项三","sid":"serial_khe7yak6","disable":false,"defaultChecked":false}]
     */
    dataSource?: IDataSource[];

    /**
     * 网关数据
     * 点击查看如何使用网关数据
     */
    remote?: string;

    /**
     * 关联表权限组数据过滤
     * 开启后，关联的选项数据将过滤掉用户所在权限组无法查看的实例的数据
     */
    reusePrivilege?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * checkboxField onChange
     * @param value 选中的值
     */
    onChange?: ({ value }: any) => void;
  }

  //
  export const CheckboxField: React.ComponentType<ICheckboxFieldProps>;

  interface ISelectFieldProps extends ICompProps {
    /**
     * 占位提示
     *
     * @default {"zh_CN":"请选择","en_US":"please select","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'linkage';

    /**
     *
     *
     */
    value?: any;

    /**
     *
     *
     */
    formula?: any;

    /**
     * 清除按钮
     *
     * @default true
     */
    hasClear?: boolean;

    /**
     * 可搜索
     *
     * @default true
     */
    showSearch?: boolean;

    /**
     * 本地过滤
     *
     * @default true
     */
    filterLocal?: boolean;

    /**
     * 无数据时显示内容
     *
     * @default {"zh_CN":"无数据","en_US":"Not Found","type":"i18n"}
     */
    notFoundContent?: string;

    /**
     * 选项类型
     *
     * 选项使用说明：custom - undefined;relate - undefined;linkage - undefined;remote - undefined;searchConfig - undefined
     * @default custom
     */
    dataSourceType?:
      | 'custom'
      | 'relate'
      | 'linkage'
      | 'remote'
      | 'searchConfig';

    /**
     * 自定义选项
     * 数据格式
     * @default [{"text":{"zh_CN":"选项一","en_US":"Option 1","type":"i18n"},"value":"选项一","sid":"serial_khe7yak4","disable":false,"defaultChecked":false},{"text":{"zh_CN":"选项二","en_US":"Option 2","type":"i18n"},"value":"选项二","sid":"serial_khe7yak5","disable":false,"defaultChecked":false},{"text":{"zh_CN":"选项三","en_US":"Option 3","type":"i18n"},"value":"选项三","sid":"serial_khe7yak6","disable":false,"defaultChecked":false}]
     */
    dataSource?: IDataSource[];

    /**
     * 数据排序
     *
     */
    relateOrderEnable?: boolean;

    /**
     * 网关数据
     * 点击查看如何使用网关数据
     */
    remote?: string;

    /**
     * 关联表权限组数据过滤
     * 开启后，关联的选项数据将过滤掉用户所在权限组无法查看的实例的数据
     */
    reusePrivilege?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * selectField onChange
     * @param value: {mixed} 选中的值
     * @param actionType: {String} 触发的方式, 'itemClick', 'enter', 'change'
     * @param item: {mixed} 选中的值的对象数据
     */
    onChange?: ({ value, actionType, item }: any) => void;

    /**
     * onVisibleChange 弹层显示隐藏变化
     * * selectField onVisibleChange
     * @param visible: {Boolean} 下拉框是否显示
     */
    onVisibleChange?: (visible: any) => void;

    /**
     * onSearch 搜索
     * * selectField onSearch
     * @param value: {String} 数据
     */
    onSearch?: (keyword: any) => void;
  }

  //
  export const SelectField: React.ComponentType<ISelectFieldProps>;

  interface IDateFieldProps extends ICompProps {
    /**
     * 占位提示
     *
     * @default {"zh_CN":"请选择","en_US":"please select","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - 自定义;variable - undefined;formula - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'variable' | 'formula' | 'linkage';

    /**
     *
     *
     */
    value?: any;

    /**
     *
     *
     */
    formula?: any;

    /**
     * 格式
     *
     * 选项使用说明：YYYY - undefined;YYYY-MM - undefined;YYYY-MM-DD - 年-月-日;YYYY-MM-DD HH:mm - 年-月-日 时:分;YYYY-MM-DD HH:mm:ss - 年-月-日 时:分:秒
     * @default YYYY-MM-DD
     */
    format?:
      | 'YYYY'
      | 'YYYY-MM'
      | 'YYYY-MM-DD'
      | 'YYYY-MM-DD HH:mm'
      | 'YYYY-MM-DD HH:mm:ss';

    /**
     * 清除按钮
     *
     * @default true
     */
    hasClear?: boolean;

    /**
     * 每次选择日期时是否重置时间
     *
     */
    resetTime?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * dateField onChange
     * @param value 选中的值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onOk 点击确认按钮
     * * dateField onOk
     * @param value 选中的值
     */
    onOk?: ({ value }: any) => void;

    /**
     * onVisibleChange 弹层展示状态变化
     * * dateField onVisibleChange
     * @param visible 弹层是否显示
     * @param reason 触发弹层显示和隐藏的来源
     */
    onVisibleChange?: (visible: any, reason: any) => void;
  }

  //
  export const DateField: React.ComponentType<IDateFieldProps>;

  interface ICascadeDateFieldProps extends ICompProps {
    /**
     * 显示格式
     *
     * 选项使用说明：YYYY - undefined;YYYY-MM - undefined;YYYY-MM-DD - 年-月-日;YYYY-MM-DD HH:mm - 年-月-日 时:分;YYYY-MM-DD HH:mm:ss - 年-月-日 时:分:秒
     * @default YYYY-MM-DD
     */
    format?:
      | 'YYYY'
      | 'YYYY-MM'
      | 'YYYY-MM-DD'
      | 'YYYY-MM-DD HH:mm'
      | 'YYYY-MM-DD HH:mm:ss';

    /**
     * 清除按钮
     *
     * @default true
     */
    hasClear?: boolean;

    /**
     * 每次选择日期时是否重置时间
     *
     */
    resetTime?: boolean;

    /**
     * 禁用日期函数
     * 点击查看文档
     */
    disabledDate?: (props: any) => void;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * dateField onChange
     * @param value 选中的值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onOk 点击确认按钮
     * * dateField onOk
     * @param value 选中的值
     */
    onOk?: ({ value }: any) => void;

    /**
     * onVisibleChange 弹层显示隐藏变化
     * * dateField onVisibleChange
     * @param visible 弹层是否显示
     * @param reason 触发弹层显示和隐藏的来源
     */
    onVisibleChange?: (visible: any, reason: any) => void;
  }

  //
  export const CascadeDateField: React.ComponentType<ICascadeDateFieldProps>;

  interface IImageFieldProps extends ICompProps {
    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'linkage';

    /**
     *
     *
     * @default {"type":"i18n","en_US":"","zh_CN":""}
     */
    value?: string;

    /**
     *
     *
     */
    formula?: any;

    /**
     * 是否开启
     * 开启后仅支持单张图片上传
     */
    aiRecognitionSwitch?: boolean;

    /**
     * AI配置
     *
     * @default {}
     */
    aiRecognitionConfig?: any;

    /**
     * 是否开启
     *
     */
    showAIImgGenerate?: boolean;

    /**
     * 图片比例
     * AI生成的图片比例
     * 选项使用说明：1:1 - 1:1;16:9 - 16:9;9:16 - 9:16
     * @default 1:1
     */
    aspectRatio?: '1:1' | '16:9' | '9:16';

    /**
     * 上传类型
     * 仅 PC 端有效
     * 选项使用说明：normal - 点击;card - 卡片;drag - 拖拽
     * @default normal
     */
    type?: 'normal' | 'card' | 'drag';

    /**
     * 列表样式
     * 仅 PC 端有效
     * 选项使用说明：text - 文字;image - 图文
     * @default image
     */
    normalListType?: 'text' | 'image';

    /**
     * 上传列表样式
     * 仅 PC 端有效
     * 选项使用说明：card - 卡片
     * @default card
     */
    cardListType?: 'card';

    /**
     * 上传列表样式
     * 仅 PC 端有效
     * 选项使用说明：text - 文字;image - 图文;card - 卡片
     * @default image
     */
    listType?: 'text' | 'image' | 'card';

    /**
     * 按钮内容
     * 仅 PC 端有效
     * @default {"zh_CN":"图片上传","en_US":"Upload","type":"i18n"}
     */
    buttonText?: string;

    /**
     * 按钮尺寸
     * 仅 PC 端有效
     * 选项使用说明：small - 小;medium - 中;large - 大
     * @default medium
     */
    buttonSize?: 'small' | 'medium' | 'large';

    /**
     * 按钮类型
     * 仅 PC 端有效
     * 选项使用说明：primary - 主要按钮;secondary - 次要按钮;normal - 常规按钮
     * @default normal
     */
    buttonType?: 'primary' | 'secondary' | 'normal';

    /**
     * 仅允许拍照上传
     * 该功能目前仅支持钉钉移动端，开启后「非钉钉移动端」会自动禁用上传功能。
     */
    onlyCameraUpload?: boolean;

    /**
     * 开启水印相机
     * 该功能目前仅支持钉钉移动端，开启后「非钉钉移动端」会自动禁用上传功能。
     */
    enableCameraWatermark?: boolean;

    /**
     * 压缩图片
     * 受钉钉客户端限制，部分版本和机型可能不支持该选项
     */
    enableCameraCompression?: boolean;

    /**
     * 水印显示日期
     *
     * @default true
     */
    enableCameraDate?: boolean;

    /**
     * 水印显示定位
     *
     * @default true
     */
    enableCameraLocation?: boolean;

    /**
     * 水印照片保存到相册
     *
     * @default true
     */
    saveCameraImageToLocal?: boolean;

    /**
     * 多选
     *
     * @default true
     */
    multiple?: boolean;

    /**
     * 上传超时
     * 设置上传超时, 单位 ms
     */
    timeout?: number;

    /**
     * 接口地址
     * 默认使用全局配置，也可自定义，数据格式可点击查看
     */
    url?: string;

    /**
     * name
     * 上传时发送给服务端的name
     */
    name?: string;

    /**
     * 额外参数
     * 上传可附带的额外参数
     * @default {}
     */
    data?: any;

    /**
     * 上传前处理
     * 上传前处理函数
     */
    beforeUpload?: (props: any) => void;

    /**
     * 数据处理
     * 数据处理函数
     */
    formatter?: (props: any) => void;

    /**
     * 上传方法
     *
     * 选项使用说明：post - post;put - put
     * @default post
     */
    method?: 'post' | 'put';

    /**
     * 最大上传文件个数
     *
     * @default 9
     */
    limit?: number;

    /**
     * 单文件最大上传大小(MB)
     *
     * @default 50
     */
    maxFileSize?: number;

    /**
     * 自动上传
     * 关闭后，需要手动调用 this.$('fieldid').getInstance().startUpload() 开始上传
     * @default true
     */
    autoUpload?: boolean;

    /**
     * 上传文件类型(多个以英文逗号 , 分隔)
     * 如 image/*
     * @default image/*
     */
    accept?: string;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * imageField onChange
     * @param value 当前值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onProgress 上传中
     * * imageField onProgress
     */
    onProgress?: () => void;

    /**
     * onSuccess 上传成功
     * * imageField onSuccess
     * @param file: {Object} 文件
     * @param value: {Array} 值
     */
    onSuccess?: (file: any, value: any) => void;

    /**
     * onError 上传失败
     * * imageField onError
     * @param file: {Object} 出错的文件
     * @param value: {Array} 当前值
     */
    onError?: (file: any, value: any) => void;

    /**
     * onSelect 选择文件
     * * imageField onSelect
     */
    onSelect?: () => void;

    /**
     * onRemove 点击移除
     * * imageField onRemove
     * @param file: {Object} 文件
     */
    onRemove?: (file: any) => void;

    /**
     * onCancel 取消上传
     * * imageField onCancel
     */
    onCancel?: () => void;

    /**
     * onDragOver 拖拽经过
     * * imageField onDragOver
     */
    onDragOver?: () => void;

    /**
     * onDragLeave 拖拽离开
     * * imageField onDragLeave
     */
    onDragLeave?: () => void;

    /**
     * onDrop 拖拽完成
     * * imageField onDrop
     */
    onDrop?: () => void;
  }

  //
  export const ImageField: React.ComponentType<IImageFieldProps>;

  interface IAttachmentFieldProps extends ICompProps {
    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'linkage';

    /**
     *
     *
     * @default {"type":"i18n","en_US":"","zh_CN":""}
     */
    value?: string;

    /**
     *
     *
     */
    formula?: any;

    /**
     * 上传类型
     * 仅 PC 端有效
     * 选项使用说明：normal - 点击;drag - 拖拽
     * @default normal
     */
    type?: 'normal' | 'drag';

    /**
     * 上传列表样式
     *
     * 选项使用说明：text - 文字
     * @default text
     */
    listType?: 'text';

    /**
     * 按钮内容
     *
     * @default {"zh_CN":"上传文件","en_US":"Upload","type":"i18n"}
     */
    buttonText?: string;

    /**
     * 定制面板
     * 自定义拖拽上传面板
     */
    customUploadPane?: (props: any) => void;

    /**
     * 按钮尺寸
     *
     * 选项使用说明：small - 小;medium - 中;large - 大
     * @default medium
     */
    buttonSize?: 'small' | 'medium' | 'large';

    /**
     * 按钮类型
     *
     * 选项使用说明：primary - 主要按钮;secondary - 次要按钮;normal - 常规按钮
     * @default normal
     */
    buttonType?: 'primary' | 'secondary' | 'normal';

    /**
     * 多选
     *
     * @default true
     */
    multiple?: boolean;

    /**
     * 允许编辑
     * 允许多人同时编辑上传的文件，支持 PPT、Excel、Word 系列文件
     */
    onlineEdit?: boolean;

    /**
     * 上传超时
     * 设置上传超时, 单位 ms
     */
    timeout?: number;

    /**
     * 接口地址
     * 默认使用全局配置，也可自定义，数据格式可点击查看
     */
    url?: string;

    /**
     * name
     * 上传时发送给服务端的name
     */
    name?: string;

    /**
     * 额外参数
     * 上传可附带的额外参数
     * @default {}
     */
    data?: any;

    /**
     * 头部信息
     * 设置上传的请求头部
     * @default {}
     */
    headers?: any;

    /**
     * 上传前处理
     * 上传前处理函数
     */
    beforeUpload?: (props: any) => void;

    /**
     * 数据处理
     * 数据处理函数
     */
    formatter?: (props: any) => void;

    /**
     * 上传方法
     *
     * 选项使用说明：post - post;put - put
     * @default post
     */
    method?: 'post' | 'put';

    /**
     * 最大上传文件个数
     *
     * @default 9
     */
    limit?: number;

    /**
     * 单文件最大上传大小(MB)
     *
     * @default 100
     */
    maxFileSize?: number;

    /**
     * 是否允许请求携带 cookie
     *
     */
    withCredentials?: boolean;

    /**
     * 自动上传
     * 关闭后，需要手动调用 this.$('fieldId').startUpload() 开始上传
     * @default true
     */
    autoUpload?: boolean;

    /**
     * 上传文件类型(多个以英文逗号 , 分隔)
     * 如 .doc, .docx, .pdf
     */
    accept?: string;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * attachmentField onChange
     * @param value 当前值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onProgress 上传中
     * * attachmentField onProgress
     */
    onProgress?: () => void;

    /**
     * onSuccess 上传成功
     * * attachmentField onSuccess
     * @param file: {Object} 文件
     * @param value: {Array} 值
     */
    onSuccess?: (file: any, value: any) => void;

    /**
     * onError 上传失败
     * * attachmentField onError
     * @param file: {Object} 出错的文件
     * @param value: {Array} 当前值
     */
    onError?: (file: any, value: any) => void;

    /**
     * onSelect 选择文件
     * * attachmentField onSelect
     */
    onSelect?: () => void;

    /**
     * onRemove 点击移除
     * * attachmentField onRemove
     * @param file: {Object} 文件
     */
    onRemove?: (file: any) => void;

    /**
     * onCancel 取消上传
     * * attachmentField onCancel
     */
    onCancel?: () => void;

    /**
     * onDragOver 拖拽经过
     * * attachmentField onDragOver
     */
    onDragOver?: () => void;

    /**
     * onDragLeave 拖拽离开
     * * attachmentField onDragLeave
     */
    onDragLeave?: () => void;

    /**
     * onDrop 拖拽完成
     * * attachmentField onDrop
     */
    onDrop?: () => void;
  }

  //
  export const AttachmentField: React.ComponentType<IAttachmentFieldProps>;

  interface IEmployeeFieldProps extends ICompProps {
    /**
     * 占位提示
     *
     * @default {"zh_CN":"请选择","en_US":"Please Select","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 可选范围
     *
     * 选项使用说明：ALL - undefined;ROLE - undefined;USER - undefined
     * @default ALL
     */
    userRangeType?: 'ALL' | 'ROLE' | 'USER';

    /**
     *
     *
     * @default []
     */
    roleRange?: any;

    /**
     *
     *
     * @default []
     */
    userRange?: any;

    /**
     * 默认值
     * 属性：value
     * 选项使用说明：custom - 自定义;variable - undefined;formula - undefined;linkage - undefined
     * @default custom
     */
    valueType?: 'custom' | 'variable' | 'formula' | 'linkage';

    /**
     *
     *
     */
    formula?: any;

    /**
     * 多选模式
     *
     */
    multiple?: boolean;

    /**
     * 清除按钮
     * 仅支持PC端
     * @default true
     */
    hasClear?: boolean;

    /**
     * 显示更多信息
     *
     * 选项使用说明：false - 无;N - 工号;true - userId
     */
    showEmplId?: 'false' | 'N' | 'true';

    /**
     * 选中关闭
     *
     */
    closeOnSelect?: boolean;

    /**
     * 选人起点
     *
     * 选项使用说明：SELF - 自己部门;TOP - 企业顶层
     * @default SELF
     */
    startWithDepartmentId?: 'SELF' | 'TOP';

    /**
     * 查看链接
     *
     * @default true
     */
    renderLinkForView?: boolean;

    /**
     * 内外链接
     *
     */
    useAliworkUrl?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 值发生变化
     * * employeeField onChange
     * @param value 当前值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onSearch 用户输入搜索关键字
     * * employeeField onSearch
     * @param value 当前值
     */
    onSearch?: (keyword: any) => void;
  }

  //
  export const EmployeeField: React.ComponentType<IEmployeeFieldProps>;

  interface IRateFieldProps extends ICompProps {
    /**
     * 默认值
     *
     */
    value?: string;

    /**
     * 评分总数
     *
     * @default 5
     */
    count?: number;

    /**
     * 半星评分
     *
     */
    allowHalf?: boolean;

    /**
     * 显示分数
     *
     */
    showGrade?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * onChange 用户点击评分时
     * * rateField onChange
     * @param value number 评分的值
     */
    onChange?: ({ value }: any) => void;

    /**
     * onHoverChange 用户hover评分时
     * * rateField onHoverChange
     * @param value number 评分的值
     */
    onHoverChange?: (value: any) => void;
  }

  //
  export const RateField: React.ComponentType<IRateFieldProps>;

  interface IAssociationFormFieldProps extends ICompProps {
    /**
     * 所属子表单的唯一标识
     *
     */
    __tableFieldId?: string;

    /**
     * 占位提示
     *
     * @default {"zh_CN":"请选择","en_US":"please select","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 显示方式
     *
     * 选项使用说明：select - 下拉;table - 表格
     * @default select
     */
    displayStyle?: 'select' | 'table';

    /**
     * 数据筛选
     *
     */
    supportDataFilter?: boolean;

    /**
     * 数据排序
     *
     */
    orderEnable?: boolean;

    /**
     * 开启填充
     *
     */
    supportDataFilling?: boolean;

    /**
     * 填充条件
     *
     * @default []
     */
    dataFillingRules?: any;

    /**
     * 允许新增
     * 允许在选择表单时新增实例数据
     * @default true
     */
    supportAdd?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * 清除按钮
     *
     * @default true
     */
    hasClear?: boolean;

    /**
     * 提交检测
     * 强制校验值是否符合筛选条件，不符合则不允许提交
     */
    validateFilter?: boolean;

    /**
     * 移动端快捷搜索
     * 设置移动端快捷搜索输入框匹配模式：主要信息字段（仅匹配关联表单设置的主要信息字段）或全部字段（仅新存储生效）
     * 选项使用说明：MainField - 主要信息字段;AllField - 全部字段
     * @default AllField
     */
    mobileQuickSearchMode?: 'MainField' | 'AllField';

    /**
     * 无数据时显示
     *
     * @default {"zh_CN":"无数据","en_US":"Not Found","type":"i18n"}
     */
    notFoundContent?: string;

    /**
     * onChange 值发生变化
     * * AssociationFormField onChange
     * @param value 当前值
     */
    onChange?: ({ value }: any) => void;
  }

  //
  export const AssociationFormField: React.ComponentType<IAssociationFormFieldProps>;

  interface IAddressFieldProps extends ICompProps {
    /**
     * 占位提示
     *
     * @default {"zh_CN":"请选择","en_US":"Please select region","type":"i18n"}
     */
    placeholder?: string;

    /**
     * 国家/地区范围
     *
     * 选项使用说明：default - 默认;custom - 自定义;all - 全部
     * @default default
     */
    countryMode?: 'default' | 'custom' | 'all';

    /**
     * 是否显示国家/地区
     *
     */
    showCountry?: boolean;

    /**
     * 类型
     *
     * 选项使用说明：ADDRESS - 省或州 / 市 / 区 / 街道 / 详细地址;STREET - 省或州 / 市 / 区 / 街道;DISTRICT - 省或州 / 市 / 区;CITY - 省或州 / 市;PROVINCE - 省或州
     * @default ADDRESS
     */
    addressType?: 'ADDRESS' | 'STREET' | 'DISTRICT' | 'CITY' | 'PROVINCE';

    /**
     * 子标题
     *
     * @default {"zh_CN":"详细地址","en_US":"Detailed Address","type":"i18n"}
     */
    subLabel?: string;

    /**
     * 输入提示
     *
     * @default {"zh_CN":"请输入详细地址","en_US":"Please input detailed address","type":"i18n"}
     */
    detailPlaceholder?: string;

    /**
     * 清除按钮
     *
     * @default true
     */
    hasClear?: boolean;

    /**
     * 快速定位(仅移动端)
     *
     * @default true
     */
    enableLocation?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 数据提交
     * 组件隐藏是否提交数据。注意：该选项是配合控件的「默认状态」属性；多端显示不受此设置项影响，不显示的设备中，无法获取到组件数据
     * 选项使用说明：DEFAULT - 仅显示时提交;ALWAYS - 始终提交
     * @default DEFAULT
     */
    submittable?: 'DEFAULT' | 'ALWAYS';

    /**
     * 值发生变化
     *  * address onChange
     * @param value 选中的地区的值
     */
    onChange?: ({ value }: any, extra: any) => void;
  }

  //
  export const AddressField: React.ComponentType<IAddressFieldProps>;

  interface IPageSectionProps extends ICompProps {
    /**
     * 显示头部
     *
     * @default true
     */
    showHeader?: boolean;

    /**
     * 分组标题
     *
     * @default {"type":"i18n","zh_CN":"分组","en_US":"Group"}
     */
    title?: string;

    /**
     * 图标
     *
     */
    icon?: any;

    /**
     * 用户提示
     *
     * @default {"type":"i18n","zh_CN":"","en_US":""}
     */
    tooltip?: string;

    /**
     * 头部分割线
     *
     * @default true
     */
    showHeadDivider?: boolean;

    /**
     * 分组样式
     *
     * @default origin
     */
    sectionHeaderStyle?: any;

    /**
     * 背景配色
     *
     * @default #0089ff
     */
    sectionHeaderBgColor?: any;

    /**
     * 标题颜色
     *
     * @default #171A1D
     */
    sectionHeaderTitleColor?: any;

    /**
     * 布局
     *
     * 可选择以下数组中的一组配置：[{"text":"origin","value":"origin","img":"https://img.alicdn.com/imgextra/i3/O1CN01TFQRuq1z4KlNLCNar_!!6000000006660-2-tps-137-72.png"},{"text":"A","value":"pcAtmosphereA","img":"https://img.alicdn.com/imgextra/i2/O1CN013dOqWD1WG86fzl9bc_!!6000000002760-2-tps-137-72.png"},{"text":"B","value":"pcAtmosphereB","img":"https://img.alicdn.com/imgextra/i4/O1CN01lavZUE1VBGHMmodq2_!!6000000002614-2-tps-137-72.png"}]
     */
    pcStyle?: any;

    /**
     * 显示边框
     *
     */
    showBorder?: boolean;

    /**
     * 外边距
     *
     */
    withMargin?: boolean;

    /**
     * 内边距
     *
     * @default true
     */
    withPadding?: boolean;

    /**
     * 布局
     *
     * 可选择以下数组中的一组配置：[{"text":"origin","value":"origin","img":"https://img.alicdn.com/imgextra/i3/O1CN01XgaY2r25Aduk72Wg7_!!6000000007486-2-tps-137-72.png"},{"text":"A","value":"mobileAtmosphereA","img":"https://img.alicdn.com/imgextra/i2/O1CN01izuH681SbQ3IMksl9_!!6000000002265-2-tps-137-72.png"}]
     */
    mobileStyle?: any;

    /**
     * 显示边框
     *
     */
    showBorderMobile?: boolean;

    /**
     * 外边距
     *
     */
    withMarginMobile?: boolean;

    /**
     * 内边距
     *
     */
    withPaddingMobile?: boolean;
  }

  //
  export const PageSection: React.ComponentType<IPageSectionProps>;

  interface IDivProps extends ICompProps {}

  //
  export const Div: React.ComponentType<IDivProps>;

  interface ITextProps extends ICompProps {
    /**
     * 内容
     *
     * @default {"zh_CN":"文本","en_US":"Tips content","type":"i18n"}
     */
    content?: string;

    /**
     * 左右间距
     *
     * 选项使用说明：0 - undefined;16 - undefined
     * @default 16
     */
    contentPaddingMobile?: '0' | '16';

    /**
     * 点击
     * * text onClick
     */
    onClick?: () => void;
  }

  //
  export const Text: React.ComponentType<ITextProps>;

  interface ILinkProps extends ICompProps {
    /**
     * 内容
     *
     * @default {"zh_CN":"这里是一个链接","en_US":"link text","type":"i18n"}
     */
    content?: string;

    /**
     * 单行截断
     *
     */
    textOverflow?: boolean;

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * 当点击时
     * * link onClick
     */
    onClick?: () => void;
  }

  //
  export const Link: React.ComponentType<ILinkProps>;

  interface ILinkBlockProps extends ICompProps {
    /**
     * 是否禁用
     *
     */
    disabled?: boolean;

    /**
     * 当点击时
     * * linkBlock onClick
     */
    onClick?: () => void;
  }

  //
  export const LinkBlock: React.ComponentType<ILinkBlockProps>;

  interface IButtonProps extends ICompProps {
    /**
     * 标题
     *
     * @default {"zh_CN":"按 钮","en_US":"Button","type":"i18n"}
     */
    content?: string;

    /**
     * 按钮类型
     *
     * @default primary
     */
    type?: any;

    /**
     * 基础图标
     *
     */
    baseIcon?: any;

    /**
     * 其他图标
     *
     */
    otherIcon?: string;

    /**
     * 加载状态
     *
     */
    loading?: boolean;

    /**
     * 加载点击
     *
     */
    triggerEventsWhenLoading?: boolean;

    /**
     * 左右间距
     *
     * 选项使用说明：0 - undefined;16 - undefined
     * @default 16
     */
    contentMarginMobile?: '0' | '16';

    /**
     * 跨列
     *
     * 选项使用说明：1 - 1列;2 - 2列
     * @default 1
     */
    __gridSpan?: '1' | '2';

    /**
     * 表单标识
     *
     */
    fieldName?: string;

    /**
     * 纯输入型组件模式
     *
     */
    dataEntryMode?: boolean;

    /**
     * onClick 点击按钮
     * * button onClick
     */
    onClick?: () => void;
  }

  //
  export const Button: React.ComponentType<IButtonProps>;
}
