{"name": "yida-project", "version": "1.0.0", "private": true, "scripts": {"build": "tsc", "lint": "eslint src --ext ts,tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^16.13.0", "react-dom": "^16.13.0", "antd": "^5.0.0", "@ant-design/icons": "^5.0.0", "chart.js": "^4.0.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.0.0", "eslint-plugin-react-hooks": "^4.0.0", "typescript": "^5.0.0"}, "yidaComponentList": ["Page", "FormContainer", "TextField", "TextAreaField", "NumberField", "RadioField", "CheckboxField", "SelectField", "DateField", "CascadeDateField", "ImageField", "AttachmentField", "EmployeeField", "DepartmentField", "RateField", "AssociationFormField", "AddressField", "PageSection", "Div", "Text", "Link", "LinkBlock", "<PERSON><PERSON>"], "mcp": "node c:\\Users\\<USER>\\.cursor\\extensions\\yida.yida-code-0.0.2\\out\\mcp\\yida\\index.js"}