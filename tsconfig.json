{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "node", "lib": ["DOM", "DOM.Iterable", "ESNext"], "jsx": "react", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "types/*": ["types/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules"]}